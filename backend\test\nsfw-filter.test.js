// Test file to demonstrate NSFW content filtering
const aiservice = require('../src/services/ai.service.js');

// Mock the external dependencies to focus on testing the filter
jest.mock('replicate');
jest.mock('imagekit');

// Extract the filter function for testing (in a real scenario, you might export it separately)
const filterNSFWContent = (prompt) => {
    const nsfwWords = [
        'nsfw', 'nude', 'naked', 'sex', 'sexual', 'porn', 'erotic', 'hentai',
        'genitalia', 'breasts', 'nipples', 'inappropriate', 'obscene', 'offensive',
        'suggestive', 'lingerie', 'bikini', 'swimsuit', 'lewd', 'fetish',
        'exposed skin', 'underwear', 'adult content', 'obscene gesture',
        'body fluids', 'explicit'
    ];

    let filteredPrompt = prompt;

    // Remove each NSFW word completely (case-insensitive)
    nsfwWords.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, 'gi'); // 'g' for global, 'i' for case-insensitive, \b for word boundaries
        filteredPrompt = filteredPrompt.replace(regex, '');
    });

    // Clean up extra spaces that might be left after removing words
    filteredPrompt = filteredPrompt.replace(/\s+/g, ' ').trim();

    return filteredPrompt;
};

describe('NSFW Content Filter', () => {
    test('should remove NSFW words completely', () => {
        const testPrompt = "Create a beautiful nude woman in lingerie";
        const filtered = filterNSFWContent(testPrompt);
        expect(filtered).toBe("Create a beautiful woman in");
    });

    test('should handle case-insensitive filtering', () => {
        const testPrompt = "NSFW content with EXPLICIT material";
        const filtered = filterNSFWContent(testPrompt);
        expect(filtered).toBe("content with material");
    });

    test('should handle multiple occurrences of the same word', () => {
        const testPrompt = "sex and more sex content";
        const filtered = filterNSFWContent(testPrompt);
        expect(filtered).toBe("and more content");
    });

    test('should not affect clean content', () => {
        const testPrompt = "A beautiful landscape with mountains and trees";
        const filtered = filterNSFWContent(testPrompt);
        expect(filtered).toBe("A beautiful landscape with mountains and trees");
    });

    test('should handle compound phrases with NSFW terms', () => {
        const testPrompt = "exposed skin in the sunlight";
        const filtered = filterNSFWContent(testPrompt);
        expect(filtered).toBe("in the sunlight");
    });

    test('should clean up extra spaces after removing words', () => {
        const testPrompt = "This   nude   content   should   be   clean";
        const filtered = filterNSFWContent(testPrompt);
        expect(filtered).toBe("This content should be clean");
    });
});

// Example usage demonstration
console.log('=== NSFW Filter Examples ===');
console.log('Original: "Create a nude woman"');
console.log('Filtered:', filterNSFWContent("Create a nude woman"));
console.log('');
console.log('Original: "Beautiful landscape with sexual content"');
console.log('Filtered:', filterNSFWContent("Beautiful landscape with sexual content"));
console.log('');
console.log('Original: "Clean prompt about nature"');
console.log('Filtered:', filterNSFWContent("Clean prompt about nature"));
console.log('');
console.log('Original: "This nsfw explicit content should be removed"');
console.log('Filtered:', filterNSFWContent("This nsfw explicit content should be removed"));
