const Replicate = require("replicate");
const ImageKit = require("imagekit");

// Initialize ImageKit (will be initialized when needed to avoid env issues)
let imagekit = null;

// NSFW content filter
const filterNSFWContent = (prompt) => {
    const nsfwWords = [
        'nsfw', 'nude', 'naked', 'sex', 'sexual', 'porn', 'erotic', 'hentai',
        'genitalia', 'breasts', 'nipples', 'inappropriate', 'obscene', 'offensive',
        'suggestive', 'lingerie', 'bikini', 'swimsuit', 'lewd', 'fetish',
        'exposed skin', 'underwear', 'adult content', 'obscene gesture',
        'body fluids', 'explicit',''
    ];

    let filteredPrompt = prompt;

    // Sort words by length (longest first) to handle multi-word phrases correctly
    const sortedWords = nsfwWords.sort((a, b) => b.length - a.length);

    // Remove each NSFW word/phrase completely (case-insensitive)
    sortedWords.forEach(word => {
        // Escape special regex characters and handle multi-word phrases
        const escapedWord = word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`\\b${escapedWord}\\b`, 'gi');
        filteredPrompt = filteredPrompt.replace(regex, '');
    });

    // Clean up extra spaces that might be left after removing words
    filteredPrompt = filteredPrompt.replace(/\s+/g, ' ').trim();

    return filteredPrompt;
};

const initializeImageKit = () => {
    if (!imagekit) {
        imagekit = new ImageKit({
            publicKey: process.env.IMAGEKIT_PUBLIC_KEY,
            privateKey: process.env.IMAGEKIT_PRIVATE_KEY,
            urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT
        });
    }
    return imagekit;
};

const aiservice = async(prompt, aspectRatio = '1:1')=>{
try {
    // Filter NSFW content from the prompt
    const filteredPrompt = filterNSFWContent(prompt);

    // Step 1: Generate image using Replicate
    const replicate = new Replicate({
        auth: process.env.AI_KEY,
    });
console.log(filteredPrompt);
    const output = await replicate.run(
        "prunaai/flux.1-dev:786b08f5ce6469390ec0cd9164bde74d02a2af548316821eba0bbeb347b421df",
        {
            input: {
                aspect_ratio: aspectRatio,
                
                prompt: filteredPrompt,
                negative_prompt:"nsfw, nude, naked, sex, sexual, porn, erotic, hentai, genitalia, breasts, nipples, inappropriate, obscene, offensive, suggestive, lingerie, bikini, swimsuit, lewd, fetish, exposed skin, underwear, adult content, obscene gesture, body fluids, explicit",

                seed: -1,
                output_format: "jpg",
                output_quality: 80,
                num_inference_steps: 28,
                guidance: 4,
                image_size: 1024,
                speed_mode: "Extra Juiced 🔥 (more speed)",
            }
        }
    );

    // Get the temporary Replicate image URL (convert URL object to string)
    const replicateImageUrl = output.url().toString();
    

    // Step 2: Upload image to ImageKit for permanent storage
    try {
        // Initialize ImageKit when needed
        const imagekitInstance = initializeImageKit();

        // Generate a unique filename
        const timestamp = Date.now();
        const fileName = `ai-generated-${timestamp}-${aspectRatio.replace(':', 'x')}.jpg`;

       
        const uploadResponse = await imagekitInstance.upload({
            file: replicateImageUrl, // ImageKit can accept URL as file input
            fileName: fileName,
            folder: '/ai-generated-images', // Organize images in a folder
            tags: ['ai-generated', `aspect-${aspectRatio}`, 'replicate']
            // Removed customMetadata as it requires predefined fields in ImageKit dashboard
        });

      
       

        // Return the permanent ImageKit URL
        return uploadResponse.url;

    } catch (uploadError) {
        console.error('❌ ImageKit upload failed:', uploadError);
        // Fallback to original Replicate URL if ImageKit upload fails
        console.log('⚠️ Falling back to Replicate URL');
        return replicateImageUrl;
    }

} catch (error) {
    console.error('❌ AI service error:', error);
    throw error; // Re-throw error so controller can handle it properly
}

}
module.exports = aiservice;